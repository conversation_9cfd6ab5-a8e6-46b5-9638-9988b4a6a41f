# Example demonstrating error metadata coloring in the formatter
#
# This example shows how metadata keys that start with "error" get their values
# colored red when colorization is enabled.
#
# Run with: mix run examples/error_metadata_coloring.exs

# Configure the logger to enable colorization and include error metadata
Application.put_env(:drops, :logger, [
  formatter: %{colorize: true},
  metadata: [:operation, :step, :error_code, :error_message, :error_details]
])

# Enable ANSI colors
Application.put_env(:elixir, :ansi_enabled, true)

# Create a sample log event with error metadata
log_event = %{
  level: :error,
  msg: {:string, "Operation failed during validation"},
  meta: %{
    operation: "UserRegistration",
    step: :validate,
    error_code: 422,
    error_message: "Invalid email format",
    error_details: %{field: :email, value: "invalid-email"}
  },
  time: System.os_time(:microsecond)
}

# Format the log using the string formatter
result = Drops.Logger.Formatter.string(log_event, nil)

IO.puts("Formatted log output:")
IO.puts(result)

IO.puts("\nNote: In the output above, the values for 'error_code', 'error_message', and 'error_details'")
IO.puts("should appear in red color when viewed in a terminal that supports ANSI colors.")
IO.puts("Regular metadata like 'operation' and 'step' will not be colored red.")
